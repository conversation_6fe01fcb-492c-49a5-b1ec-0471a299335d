import apiService from "../api/api-service";
import axiosInstance from "../api/axiosInstance";
import { Booking, CreateBookingRequest, UpdateBookingRequest, BookingSearchFilters } from "../models/booking.model";
import { BookingApiResponse } from "./bookings.model";

// Get all bookings

const bookingBaseUrl = 'api/v1'

export const getAllBookingApi = async () : Promise<BookingApiResponse[]> => {
  return apiService.getbooking<BookingApiResponse[]>(`${bookingBaseUrl}/booking`)
}

// Get booking by ID
export const getBookingById = async (id: string, retries = 3): Promise<Booking> => {
  try {
    const response = await apiService.getbooking<Booking>(`/bookings/${id}/`);
    return response.data;
  } catch (error: any) {
    console.error('Error fetching booking by ID:', error);

    // Retry logic for network errors
    if (retries > 0 && (error.code === 'NETWORK_ERROR' || error.response?.status >= 500)) {
      console.log(`Retrying... ${retries} attempts left`);
      await new Promise(resolve => setTimeout(resolve, 1000));
      return getBookingById(id, retries - 1);
    }

    // Return mock data as fallback
    const mockBookings = getMockBookings();
    const booking = mockBookings.find(b => b.id === id);
    if (!booking) {
      throw new Error(`Booking with ID ${id} not found`);
    }
    return booking;
  }
};

// Create new booking
export const createBooking = async (bookingData: CreateBookingRequest, retries = 3): Promise<Booking> => {
  try {
    const response = await apiService.postbooking<Booking>('/bookings/', bookingData);
    return response.data;
  } catch (error: any) {
    console.error('Error creating booking:', error);

    // Retry logic for network errors
    if (retries > 0 && (error.code === 'NETWORK_ERROR' || error.response?.status >= 500)) {
      console.log(`Retrying... ${retries} attempts left`);
      await new Promise(resolve => setTimeout(resolve, 1000));
      return createBooking(bookingData, retries - 1);
    }

    // Create mock booking as fallback
    const mockBooking: Booking = {
      id: Date.now().toString(),
      bookingReference: `BK${Date.now()}`,
      ...bookingData,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    };
    return mockBooking;
  }
};

// Update booking
export const updateBooking = async (id: string, bookingData: UpdateBookingRequest, retries = 3): Promise<Booking> => {
  try {
    const response = await apiService.putbooking<Booking>(`/bookings/${id}/`, bookingData);
    return response.data;
  } catch (error: any) {
    console.error('Error updating booking:', error);

    // Retry logic for network errors
    if (retries > 0 && (error.code === 'NETWORK_ERROR' || error.response?.status >= 500)) {
      console.log(`Retrying... ${retries} attempts left`);
      await new Promise(resolve => setTimeout(resolve, 1000));
      return updateBooking(id, bookingData, retries - 1);
    }

    // Return updated mock booking as fallback
    const existingBooking = await getBookingById(id);
    const updatedBooking: Booking = {
      ...existingBooking,
      ...bookingData,
      updatedAt: new Date().toISOString(),
    };
    return updatedBooking;
  }
};

// Cancel booking
export const cancelBooking = async (id: string, reason?: string, retries = 3): Promise<Booking> => {
  try {
    const response = await apiService.postbooking<Booking>(`/bookings/${id}/cancel/`, { reason });
    return response.data;
  } catch (error: any) {
    console.error('Error cancelling booking:', error);

    // Retry logic for network errors
    if (retries > 0 && (error.code === 'NETWORK_ERROR' || error.response?.status >= 500)) {
      console.log(`Retrying... ${retries} attempts left`);
      await new Promise(resolve => setTimeout(resolve, 1000));
      return cancelBooking(id, reason, retries - 1);
    }

    // Return cancelled mock booking as fallback
    const existingBooking = await getBookingById(id);
    const cancelledBooking: Booking = {
      ...existingBooking,
      status: 'cancelled',
      cancellationReason: reason,
      cancelledAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    };
    return cancelledBooking;
  }
};

// Update booking status
export const updateBookingStatus = async (id: string, status: Booking['status'], retries = 3): Promise<Booking> => {
  try {
    const response = await axiosInstance.patch<Booking>(`/bookings/${id}/status/`, { status });
    return response.data;
  } catch (error: any) {
    console.error('Error updating booking status:', error);

    // Retry logic for network errors
    if (retries > 0 && (error.code === 'NETWORK_ERROR' || error.response?.status >= 500)) {
      console.log(`Retrying... ${retries} attempts left`);
      await new Promise(resolve => setTimeout(resolve, 1000));
      return updateBookingStatus(id, status, retries - 1);
    }

    // Return updated mock booking as fallback
    const existingBooking = await getBookingById(id);
    const updatedBooking: Booking = {
      ...existingBooking,
      status,
      updatedAt: new Date().toISOString(),
    };
    return updatedBooking;
  }
};

// Search bookings
export const searchBookings = async (filters: BookingSearchFilters, retries = 3): Promise<Booking[]> => {
  try {
    const params = new URLSearchParams();
    Object.keys(filters).forEach(key => {
      const value = filters[key as keyof BookingSearchFilters];
      if (value) params.append(key, value.toString());
    });

    const response = await axiosInstance.get<Booking[]>(`/bookings/search/?${params.toString()}`);
    return response.data;
  } catch (error: any) {
    console.error('Error searching bookings:', error);

    // Retry logic for network errors
    if (retries > 0 && (error.code === 'NETWORK_ERROR' || error.response?.status >= 500)) {
      console.log(`Retrying... ${retries} attempts left`);
      await new Promise(resolve => setTimeout(resolve, 1000));
      return searchBookings(filters, retries - 1);
    }

    // Return filtered mock data as fallback
    const mockBookings = getMockBookings();
    return mockBookings.filter(booking => {
      if (filters.search) {
        const searchLower = filters.search.toLowerCase();
        if (!booking.bookingReference.toLowerCase().includes(searchLower) &&
            !booking.guestName.toLowerCase().includes(searchLower) &&
            !booking.hotelName.toLowerCase().includes(searchLower)) {
          return false;
        }
      }
      if (filters.status && booking.status !== filters.status) return false;
      if (filters.hotelId && booking.hotelId !== filters.hotelId) return false;
      if (filters.checkInFrom && booking.checkInDate < filters.checkInFrom) return false;
      if (filters.checkInTo && booking.checkInDate > filters.checkInTo) return false;
      return true;
    });
  }
};

// Get bookings by hotel
export const getBookingsByHotel = async (hotelId: string, retries = 3): Promise<Booking[]> => {
  try {
    const response = await axiosInstance.get<Booking[]>(`/hotels/${hotelId}/bookings/`);
    return response.data;
  } catch (error: any) {
    console.error('Error fetching bookings by hotel:', error);

    // Retry logic for network errors
    if (retries > 0 && (error.code === 'NETWORK_ERROR' || error.response?.status >= 500)) {
      console.log(`Retrying... ${retries} attempts left`);
      await new Promise(resolve => setTimeout(resolve, 1000));
      return getBookingsByHotel(hotelId, retries - 1);
    }

    // Return filtered mock data as fallback
    const mockBookings = getMockBookings();
    return mockBookings.filter(booking => booking.hotelId === hotelId);
  }
};

// Get booking statistics
export const getBookingStats = async (retries = 3): Promise<any> => {
  try {
    const response = await axiosInstance.get('/bookings/stats/');
    return response.data;
  } catch (error: any) {
    console.error('Error fetching booking stats:', error);

    // Retry logic for network errors
    if (retries > 0 && (error.code === 'NETWORK_ERROR' || error.response?.status >= 500)) {
      console.log(`Retrying... ${retries} attempts left`);
      await new Promise(resolve => setTimeout(resolve, 1000));
      return getBookingStats(retries - 1);
    }

    // Return mock stats as fallback
    const mockBookings = getMockBookings();
    return {
      totalBookings: mockBookings.length,
      confirmedBookings: mockBookings.filter(b => b.status === 'confirmed').length,
      cancelledBookings: mockBookings.filter(b => b.status === 'cancelled').length,
      totalRevenue: mockBookings.reduce((sum, b) => sum + b.totalAmount, 0)
    };
  }
};

// Mock data for fallback
const getMockBookings = (): Booking[] => [
  {
    id: '1',
    bookingReference: 'BK2024001',
    hotelId: '1',
    hotelName: 'Grand Palace Resort & Spa',
    roomId: '1',
    roomName: 'Deluxe Ocean View',
    guestName: 'John Smith',
    guestEmail: '<EMAIL>',
    guestPhone: '****** 123 4567',
    checkInDate: '2024-02-15',
    checkOutDate: '2024-02-18',
    nights: 3,
    adults: 2,
    children: 0,
    status: 'confirmed',
    totalAmount: 750.00,
    currency: 'USD',
    paymentStatus: 'paid',
    bookingSource: 'direct',
    specialRequests: 'Late check-in requested',
    createdAt: '2024-01-15T10:30:00Z',
    updatedAt: '2024-01-15T10:30:00Z'
  },
  {
    id: '2',
    bookingReference: 'BK2024002',
    hotelId: '2',
    hotelName: 'Seaside Business Hotel',
    roomId: '2',
    roomName: 'Superior City View',
    guestName: 'Sarah Johnson',
    guestEmail: '<EMAIL>',
    guestPhone: '+65 9123 4567',
    checkInDate: '2024-02-20',
    checkOutDate: '2024-02-22',
    nights: 2,
    adults: 1,
    children: 0,
    status: 'confirmed',
    totalAmount: 360.00,
    currency: 'USD',
    paymentStatus: 'pending',
    bookingSource: 'booking.com',
    createdAt: '2024-01-16T14:20:00Z',
    updatedAt: '2024-01-16T14:20:00Z'
  },
  {
    id: '3',
    bookingReference: 'BK2024003',
    hotelId: '1',
    hotelName: 'Grand Palace Resort & Spa',
    roomId: '3',
    roomName: 'Family Suite',
    guestName: 'Michael Brown',
    guestEmail: '<EMAIL>',
    guestPhone: '+44 20 7123 4567',
    checkInDate: '2024-02-25',
    checkOutDate: '2024-02-28',
    nights: 3,
    adults: 2,
    children: 2,
    status: 'cancelled',
    totalAmount: 1050.00,
    currency: 'USD',
    paymentStatus: 'refunded',
    bookingSource: 'expedia',
    cancellationReason: 'Change of travel plans',
    cancelledAt: '2024-01-18T09:15:00Z',
    createdAt: '2024-01-17T16:45:00Z',
    updatedAt: '2024-01-18T09:15:00Z'
  }
];
